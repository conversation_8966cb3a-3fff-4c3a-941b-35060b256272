# GitHub Repository Root Files Guide: What Can Be Moved

This guide helps you organize your repository by moving as much as possible out of the root directory while preserving GitHub's special features.

## 🚫 Files That MUST Stay in Root

These files **cannot be moved** without losing GitHub functionality:

### Essential Files
- **README.md** - GitHub displays this on your repo's main page
- **LICENSE** - Required in root for GitHub to show license badge/info
- **.gitignore** - G<PERSON> needs this in root to function properly
- **CITATION.cff** - Only recognized in root for "Cite this repository" button

### Essential Folders
- **.github/** - All GitHub-specific configurations must stay here
  - `.github/workflows/` - GitHub Actions
  - `.github/ISSUE_TEMPLATE/` - Issue templates
  - `.github/PULL_REQUEST_TEMPLATE/` - PR templates
  - `.github/FUNDING.yml` - Sponsors button
  - `.github/dependabot.yml` - Dependabot config

## ✅ Files You CAN Move to `/documentation`

Move these files freely without breaking anything:

### Documentation Files
- API documentation
- Architecture diagrams
- Development guides
- User manuals
- Design documents
- Meeting notes
- Installation guides
- Configuration guides
- Examples and tutorials
- Any `*.md` files except those listed above

### Development Files (can go to `/docs` or `/documentation`)
- Style guides
- Testing documentation
- Deployment guides
- Database schemas
- Infrastructure documentation

## ⚠️ Files You CAN Move (But Shouldn't)

These files *can* technically be moved to `.github/`, `docs/`, or `/documentation`, but you'll lose GitHub features:

| File | Lost Feature if Moved | Recommendation |
|------|----------------------|----------------|
| **CODE_OF_CONDUCT.md** | Won't show in Community Standards | Keep in root |
| **CONTRIBUTING.md** | Won't auto-link in PRs/issues | Keep in root |
| **SECURITY.md** | Won't show in Security tab | Keep in root |
| **SUPPORT.md** | Won't show in Support section | Keep in root |
| **CHANGELOG.md** | No GitHub feature, but convention | Keep in root |

## 📁 Recommended Repository Structure

```
/
├── README.md              # Required in root
├── LICENSE               # Required in root
├── .gitignore           # Required in root
├── CITATION.cff         # Required in root (if using citations)
├── CODE_OF_CONDUCT.md   # Recommended in root
├── CONTRIBUTING.md      # Recommended in root
├── SECURITY.md          # Recommended in root
├── SUPPORT.md           # Recommended in root
├── CHANGELOG.md         # Recommended in root
├── .github/             # Required in root
│   ├── workflows/
│   ├── ISSUE_TEMPLATE/
│   ├── PULL_REQUEST_TEMPLATE.md
│   └── FUNDING.yml
└── documentation/       # Your documentation folder
    ├── getting-started/
    │   ├── installation.md
    │   ├── configuration.md
    │   └── quickstart.md
    ├── guides/
    │   ├── user-guide.md
    │   ├── developer-guide.md
    │   └── admin-guide.md
    ├── api/
    │   ├── reference.md
    │   ├── examples.md
    │   └── authentication.md
    ├── architecture/
    │   ├── overview.md
    │   ├── database-schema.md
    │   └── system-design.md
    └── contributing/
        ├── development-setup.md
        ├── testing.md
        ├── code-style.md
        └── pull-request-process.md
```

## 🔗 README.md Best Practices

Keep your root README.md clean and link to detailed docs:

```markdown
# Project Name

Brief project description and badges here.

## Quick Start

```bash
npm install your-project
npm start
```

## Documentation

- 📖 **[User Guide](./documentation/guides/user-guide.md)** - For end users
- 🔧 **[API Reference](./documentation/api/reference.md)** - Complete API docs
- 💻 **[Developer Guide](./documentation/guides/developer-guide.md)** - For contributors
- 🏗️ **[Architecture](./documentation/architecture/overview.md)** - System design

## Contributing

Please read our [Contributing Guidelines](CONTRIBUTING.md) and [Code of Conduct](CODE_OF_CONDUCT.md).

## License

This project is licensed under the [MIT License](LICENSE).
```

## 💡 Additional Tips

### For Organizations
- Create a `.github` repository in your organization for default community health files
- These will apply to all repos that don't have their own versions

### For GitHub Pages
- If using GitHub Pages without Jekyll, keep `.nojekyll` file in root
- Documentation can still be in `/docs` folder for GitHub Pages

### For Package Managers
- Keep package files in root: `package.json`, `Gemfile`, `requirements.txt`, etc.
- These are expected in root by their respective ecosystems

### For CI/CD
- Some CI systems expect config files in root (`.travis.yml`, `appveyor.yml`)
- Check your CI documentation before moving these

## 🎯 Summary

By following this guide, you can move approximately **80-90%** of your documentation out of the root directory while maintaining all GitHub features. The root stays clean with only essential files, while your detailed documentation lives organized in the `/documentation` folder.